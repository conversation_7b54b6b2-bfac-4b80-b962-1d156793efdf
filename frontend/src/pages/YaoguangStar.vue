<template>
  <div class="yaoguang-star-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">
        <i class="fas fa-star"></i>
        瑶光星 - 量化研究自动化平台
      </h1>
      <p class="page-subtitle">学习训练专家 | 因子研究 | 自动化量化研究</p>
    </div>

    <!-- 统计卡片区域 -->
    <div class="statistics-grid">
      <!-- 学习总次数 -->
      <div class="stat-card learning-card" @click="showLogs('learning')">
        <div class="stat-icon">
          <i class="fas fa-graduation-cap"></i>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ statistics.learning_total }}</div>
          <div class="stat-label">学习总次数</div>
          <div class="stat-today">今日: {{ statistics.learning_today }}</div>
        </div>
      </div>

      <!-- 因子研究总次数 -->
      <div class="stat-card factor-card" @click="showLogs('factor_research')">
        <div class="stat-icon">
          <i class="fas fa-calculator"></i>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ statistics.factor_research_total }}</div>
          <div class="stat-label">因子研究总次数</div>
          <div class="stat-today">今日: {{ statistics.factor_research_today }}</div>
        </div>
      </div>

      <!-- 研究次数 -->
      <div class="stat-card research-card" @click="showLogs('research')">
        <div class="stat-icon">
          <i class="fas fa-search"></i>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ statistics.research_total }}</div>
          <div class="stat-label">研究次数</div>
          <div class="stat-today">今日: {{ statistics.research_today }}</div>
        </div>
      </div>

      <!-- 练习次数 -->
      <div class="stat-card practice-card" @click="showLogs('practice')">
        <div class="stat-icon">
          <i class="fas fa-dumbbell"></i>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ statistics.practice_total }}</div>
          <div class="stat-label">练习次数</div>
          <div class="stat-today">今日: {{ statistics.practice_today }}</div>
        </div>
      </div>

      <!-- 因子总个数 -->
      <div class="stat-card factor-count-card">
        <div class="stat-icon">
          <i class="fas fa-cubes"></i>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ statistics.factor_count_total }}</div>
          <div class="stat-label">因子总个数</div>
          <div class="stat-today">Alpha158因子</div>
        </div>
      </div>

      <!-- 数据质量 -->
      <div class="stat-card quality-card" @click="showLogs('data_quality')">
        <div class="stat-icon">
          <i class="fas fa-shield-alt"></i>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ statistics.data_quality_total }}</div>
          <div class="stat-label">数据质量检查</div>
          <div class="stat-today">今日: {{ statistics.data_quality_today }}</div>
        </div>
      </div>
    </div>

    <!-- 学习模式控制面板 -->
    <div class="learning-control-panel">
      <div class="panel-header">
        <h3>🎓 瑶光星学习模式</h3>
        <div class="panel-controls">
          <button
            class="btn btn-success"
            @click="startLearningMode"
            :disabled="learningStatus.session_active || learningLoading"
          >
            <i class="fas fa-play"></i>
            启动学习模式
          </button>
          <button
            class="btn btn-warning"
            @click="stopLearningMode"
            :disabled="!learningStatus.session_active || learningLoading"
          >
            <i class="fas fa-stop"></i>
            停止学习
          </button>
          <button
            class="btn btn-info"
            @click="refreshLearningStatus"
            :disabled="learningLoading"
          >
            <i class="fas fa-sync"></i>
            刷新状态
          </button>
        </div>
      </div>

      <!-- 学习状态显示 -->
      <div class="learning-status-grid">
        <div class="status-item">
          <span class="status-label">学习状态:</span>
          <span :class="['status-value', learningStatus.session_active ? 'running' : 'stopped']">
            {{ learningStatus.session_active ? '学习中' : '未启动' }}
          </span>
        </div>
        <div class="status-item">
          <span class="status-label">当前会话:</span>
          <span class="status-value">{{ learningStatus.session_id || '-' }}</span>
        </div>
        <div class="status-item">
          <span class="status-label">处理股票:</span>
          <span class="status-value">{{ learningStatus.processed_stocks || 0 }}只</span>
        </div>
        <div class="status-item">
          <span class="status-label">学习进度:</span>
          <span class="status-value">{{ learningStatus.learning_progress || 0 }}%</span>
        </div>
        <div class="status-item">
          <span class="status-label">当前步骤:</span>
          <span class="status-value">{{ learningStatus.current_step || '-' }}</span>
        </div>
        <div class="status-item">
          <span class="status-label">开始时间:</span>
          <span class="status-value">{{ formatTime(learningStatus.start_time) }}</span>
        </div>
      </div>

      <!-- 实时学习日志 -->
      <div class="real-time-logs">
        <div class="logs-header">
          <h4>📋 实时学习日志</h4>
          <button class="btn btn-sm" @click="clearLogs">
            <i class="fas fa-trash"></i>
            清空日志
          </button>
        </div>
        <div class="logs-container" ref="logsContainer">
          <div v-if="realTimeLogs.length === 0" class="no-logs">
            暂无学习日志
          </div>
          <div v-else class="log-entries">
            <div
              v-for="(log, index) in realTimeLogs"
              :key="index"
              :class="['log-entry', log.level]"
            >
              <span class="log-time">{{ formatTime(log.timestamp) }}</span>
              <span class="log-level">{{ log.level.toUpperCase() }}</span>
              <span class="log-message">{{ log.message }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 自动化状态 -->
    <div class="automation-status">
      <div class="status-header">
        <h3>🤖 自动化状态</h3>
        <div class="status-controls">
          <button
            class="btn btn-primary"
            @click="toggleAutomation"
            :disabled="automationLoading"
          >
            <i :class="automationStatus.is_running ? 'fas fa-stop' : 'fas fa-play'"></i>
            {{ automationStatus.is_running ? '停止自动化' : '启动自动化' }}
          </button>
        </div>
      </div>

      <div class="status-grid">
        <div class="status-item">
          <span class="status-label">运行状态:</span>
          <span :class="['status-value', automationStatus.is_running ? 'running' : 'stopped']">
            {{ automationStatus.is_running ? '运行中' : '已停止' }}
          </span>
        </div>
        <div class="status-item">
          <span class="status-label">总循环数:</span>
          <span class="status-value">{{ automationStatus.total_cycles }}</span>
        </div>
        <div class="status-item">
          <span class="status-label">成功率:</span>
          <span class="status-value">{{ automationStatus.success_rate }}%</span>
        </div>
        <div class="status-item">
          <span class="status-label">最后执行:</span>
          <span class="status-value">{{ formatTime(automationStatus.last_cycle) }}</span>
        </div>
      </div>
    </div>

    <!-- 日志对话框 -->
    <div v-if="showLogDialog" class="log-dialog-overlay" @click="closeLogDialog">
      <div class="log-dialog" @click.stop>
        <div class="log-header">
          <h3>{{ getLogTitle(currentLogType) }}</h3>
          <button class="close-btn" @click="closeLogDialog">
            <i class="fas fa-times"></i>
          </button>
        </div>

        <div class="log-content">
          <div v-if="logsLoading" class="loading">
            <i class="fas fa-spinner fa-spin"></i>
            加载中...
          </div>

          <div v-else-if="logs.length === 0" class="no-logs">
            暂无{{ getLogTitle(currentLogType) }}记录
          </div>

          <div v-else class="log-list">
            <div
              v-for="log in logs"
              :key="log.id"
              class="log-item"
              @click="toggleLogDetails(log.id)"
            >
              <div class="log-summary">
                <div class="log-time">{{ formatTime(log.timestamp) }}</div>
                <div class="log-status" :class="log.status">{{ log.status }}</div>
                <div class="log-summary-text">{{ log.summary }}</div>
                <i class="fas fa-chevron-down expand-icon" :class="{ expanded: expandedLogs.includes(log.id) }"></i>
              </div>

              <div v-if="expandedLogs.includes(log.id)" class="log-details">
                <!-- 因子研究详细报告 -->
                <div v-if="currentLogType === 'factor_research'" class="factor-research-report">
                  <div class="report-section">
                    <h4>📊 因子研究报告</h4>
                    <div class="report-grid">
                      <div class="report-item">
                        <span class="label">研究股票:</span>
                        <span class="value">{{ log.details.factor_results ? Object.keys(log.details.factor_results).join(', ') : 'N/A' }}</span>
                      </div>
                      <div class="report-item">
                        <span class="label">股票数量:</span>
                        <span class="value">{{ log.details.total_stocks || 0 }}只</span>
                      </div>
                      <div class="report-item">
                        <span class="label">因子总数:</span>
                        <span class="value">{{ log.details.total_factors || 0 }}个</span>
                      </div>
                      <div class="report-item">
                        <span class="label">RD-Agent状态:</span>
                        <span class="value">{{ log.details.rd_agent_results?.success ? '✅ 成功' : '❌ 失败' }}</span>
                      </div>
                    </div>

                    <div v-if="log.details.factor_results" class="factor-details">
                      <h5>📈 各股票因子详情</h5>
                      <div v-for="(result, stockCode) in log.details.factor_results" :key="stockCode" class="stock-factor">
                        <div class="stock-header">{{ stockCode }}</div>
                        <div class="factor-stats">
                          <span>因子数: {{ result.factor_count }}</span>
                          <span>数据点: {{ result.data_points }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 学习总结详细报告 -->
                <div v-else-if="currentLogType === 'learning'" class="learning-report">
                  <div class="report-section">
                    <h4>📚 学习总结报告</h4>
                    <div class="report-grid">
                      <div class="report-item">
                        <span class="label">完成任务:</span>
                        <span class="value">{{ log.details.learning_report?.task_summary?.completed_tasks || 0 }}</span>
                      </div>
                      <div class="report-item">
                        <span class="label">总任务数:</span>
                        <span class="value">{{ log.details.learning_report?.task_summary?.total_tasks || 0 }}</span>
                      </div>
                      <div class="report-item">
                        <span class="label">成功率:</span>
                        <span class="value">{{ calculateSuccessRate(log.details.learning_report?.task_summary) }}%</span>
                      </div>
                      <div class="report-item">
                        <span class="label">学习效率:</span>
                        <span class="value">{{ log.details.learning_report?.performance_metrics?.automation_efficiency || 0 }}</span>
                      </div>
                    </div>

                    <div v-if="log.details.learning_report?.key_learnings" class="key-learnings">
                      <h5>💡 关键学习内容</h5>
                      <ul>
                        <li v-for="(learning, index) in log.details.learning_report.key_learnings" :key="index">
                          {{ learning }}
                        </li>
                      </ul>
                    </div>

                    <div v-if="log.details.learning_report?.recommendations" class="recommendations">
                      <h5>🎯 改进建议</h5>
                      <ul>
                        <li v-for="(rec, index) in log.details.learning_report.recommendations" :key="index">
                          {{ rec }}
                        </li>
                      </ul>
                    </div>
                  </div>
                </div>

                <!-- 其他类型显示原始JSON -->
                <div v-else class="raw-details">
                  <pre>{{ JSON.stringify(log.details, null, 2) }}</pre>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="log-footer">
          <div class="log-pagination">
            <button
              class="btn btn-sm"
              @click="loadMoreLogs"
              :disabled="logsLoading"
            >
              加载更多
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted, computed } from 'vue'
import axios from 'axios'

export default {
  name: 'YaoguangStar',
  setup() {
    // 响应式数据
    const statistics = reactive({
      learning_total: 0,
      factor_research_total: 0,
      research_total: 0,
      practice_total: 0,
      factor_count_total: 0,
      data_quality_total: 0,
      learning_today: 0,
      factor_research_today: 0,
      research_today: 0,
      practice_today: 0,
      data_quality_today: 0
    })

    const automationStatus = reactive({
      is_running: false,
      total_cycles: 0,
      success_rate: 0,
      last_cycle: null
    })

    const learningStatus = reactive({
      session_active: false,
      session_id: null,
      processed_stocks: 0,
      learning_progress: 0,
      current_step: null,
      start_time: null,
      current_stock: null
    })

    const showLogDialog = ref(false)
    const currentLogType = ref('')
    const logs = ref([])
    const logsLoading = ref(false)
    const automationLoading = ref(false)
    const learningLoading = ref(false)
    const expandedLogs = ref([])
    const realTimeLogs = ref([])
    const logsContainer = ref(null)

    // 方法
    const loadStatistics = async () => {
      try {
        const response = await axios.get('/api/yaoguang-star/statistics/overview')
        if (response.data.success) {
          const data = response.data.data
          Object.assign(statistics, data.total_statistics)
          Object.assign(statistics, data.daily_statistics)
          Object.assign(automationStatus, data.automation_status)
        }
      } catch (error) {
        console.error('加载统计数据失败:', error)
      }
    }

    const showLogs = async (logType) => {
      currentLogType.value = logType
      showLogDialog.value = true
      await loadLogs(logType)
    }

    const loadLogs = async (logType, offset = 0) => {
      logsLoading.value = true
      try {
        const response = await axios.get(`/api/yaoguang-star/logs/${logType}`, {
          params: { limit: 20, offset }
        })
        
        if (response.data.success) {
          if (offset === 0) {
            logs.value = response.data.data.logs
          } else {
            logs.value.push(...response.data.data.logs)
          }
        }
      } catch (error) {
        console.error('加载日志失败:', error)
      } finally {
        logsLoading.value = false
      }
    }

    const loadMoreLogs = () => {
      loadLogs(currentLogType.value, logs.value.length)
    }

    const closeLogDialog = () => {
      showLogDialog.value = false
      logs.value = []
      expandedLogs.value = []
    }

    const toggleLogDetails = (logId) => {
      const index = expandedLogs.value.indexOf(logId)
      if (index > -1) {
        expandedLogs.value.splice(index, 1)
      } else {
        expandedLogs.value.push(logId)
      }
    }

    const toggleAutomation = async () => {
      automationLoading.value = true
      try {
        const endpoint = automationStatus.is_running 
          ? '/api/yaoguang-automation/stop'
          : '/api/yaoguang-automation/start'
        
        const response = await axios.post(endpoint)
        
        if (response.data.success) {
          automationStatus.is_running = !automationStatus.is_running
          await loadStatistics() // 重新加载状态
        }
      } catch (error) {
        console.error('切换自动化状态失败:', error)
      } finally {
        automationLoading.value = false
      }
    }

    const getLogTitle = (logType) => {
      const titles = {
        learning: '学习日志',
        factor_research: '因子研究日志',
        research: '研究日志',
        practice: '练习日志',
        data_quality: '数据质量日志'
      }
      return titles[logType] || '日志'
    }

    const formatTime = (timestamp) => {
      if (!timestamp) return '-'
      return new Date(timestamp).toLocaleString('zh-CN')
    }

    const calculateSuccessRate = (taskSummary) => {
      if (!taskSummary || !taskSummary.total_tasks || taskSummary.total_tasks === 0) {
        return 0
      }
      return Math.round((taskSummary.completed_tasks / taskSummary.total_tasks) * 100)
    }

    // 学习模式相关方法
    const startLearningMode = async () => {
      learningLoading.value = true
      addLog('info', '🚀 启动瑶光星学习模式...')

      try {
        const response = await axios.post('/api/yaoguang-star/learning/start', {
          stocks_per_session: 1,
          data_years: 1,
          strategy_testing_enabled: true,
          learning_mode: "comprehensive",
          session_duration_minutes: 10,
          target_stocks: ["000001.XSHE"],
          enable_real_trading: false,
          enable_factor_generation: true,
          enable_multi_role_collaboration: true
        })

        if (response.data.success) {
          Object.assign(learningStatus, response.data.data)
          addLog('success', `✅ 学习模式启动成功: ${response.data.data.session_id}`)

          // 开始轮询学习状态
          startLearningStatusPolling()
        } else {
          addLog('error', `❌ 学习模式启动失败: ${response.data.error}`)
        }
      } catch (error) {
        console.error('启动学习模式失败:', error)
        addLog('error', `❌ 学习模式启动异常: ${error.message}`)
      } finally {
        learningLoading.value = false
      }
    }

    const stopLearningMode = async () => {
      learningLoading.value = true
      addLog('info', '🛑 停止瑶光星学习模式...')

      try {
        const response = await axios.post('/api/yaoguang-star/learning/stop')

        if (response.data.success) {
          learningStatus.session_active = false
          addLog('success', '✅ 学习模式已停止')

          // 停止轮询
          stopLearningStatusPolling()

          // 获取学习总结
          if (learningStatus.session_id) {
            await getLearningSessionSummary(learningStatus.session_id)
          }
        } else {
          addLog('error', `❌ 停止学习模式失败: ${response.data.error}`)
        }
      } catch (error) {
        console.error('停止学习模式失败:', error)
        addLog('error', `❌ 停止学习模式异常: ${error.message}`)
      } finally {
        learningLoading.value = false
      }
    }

    const refreshLearningStatus = async () => {
      learningLoading.value = true

      try {
        const response = await axios.get('/api/yaoguang-star/learning/status')

        if (response.data.success) {
          Object.assign(learningStatus, response.data.data)
          addLog('info', '🔄 学习状态已刷新')
        } else {
          addLog('error', `❌ 获取学习状态失败: ${response.data.error}`)
        }
      } catch (error) {
        console.error('获取学习状态失败:', error)
        addLog('error', `❌ 获取学习状态异常: ${error.message}`)
      } finally {
        learningLoading.value = false
      }
    }

    const getLearningSessionSummary = async (sessionId) => {
      try {
        const response = await axios.get(`/api/yaoguang-star/learning/summary/${sessionId}`)

        if (response.data.success) {
          const summary = response.data.data
          addLog('success', `📋 学习总结: 处理${summary.processed_stocks}只股票, 生成${summary.generated_factors}个因子, 执行${summary.executed_trades}笔交易`)
          addLog('info', `⏱️ 学习时长: ${summary.duration}`)
        }
      } catch (error) {
        console.error('获取学习总结失败:', error)
        addLog('error', `❌ 获取学习总结失败: ${error.message}`)
      }
    }

    // 实时日志相关方法
    const addLog = (level, message) => {
      const log = {
        timestamp: new Date().toISOString(),
        level: level,
        message: message
      }

      realTimeLogs.value.unshift(log)

      // 限制日志数量
      if (realTimeLogs.value.length > 100) {
        realTimeLogs.value = realTimeLogs.value.slice(0, 100)
      }

      // 自动滚动到最新日志
      setTimeout(() => {
        if (logsContainer.value) {
          logsContainer.value.scrollTop = 0
        }
      }, 100)
    }

    const clearLogs = () => {
      realTimeLogs.value = []
      addLog('info', '📝 日志已清空')
    }

    // 学习状态轮询
    let learningStatusInterval = null

    const startLearningStatusPolling = () => {
      if (learningStatusInterval) {
        clearInterval(learningStatusInterval)
      }

      learningStatusInterval = setInterval(async () => {
        if (learningStatus.session_active) {
          await refreshLearningStatus()
        } else {
          stopLearningStatusPolling()
        }
      }, 5000) // 每5秒轮询一次
    }

    const stopLearningStatusPolling = () => {
      if (learningStatusInterval) {
        clearInterval(learningStatusInterval)
        learningStatusInterval = null
      }
    }

    // 生命周期
    onMounted(() => {
      loadStatistics()
      // 定时刷新统计数据
      setInterval(loadStatistics, 30000) // 30秒刷新一次
    })

    return {
      statistics,
      automationStatus,
      learningStatus,
      showLogDialog,
      currentLogType,
      logs,
      logsLoading,
      automationLoading,
      learningLoading,
      expandedLogs,
      realTimeLogs,
      logsContainer,
      showLogs,
      closeLogDialog,
      toggleLogDetails,
      loadMoreLogs,
      toggleAutomation,
      startLearningMode,
      stopLearningMode,
      refreshLearningStatus,
      addLog,
      clearLogs,
      getLogTitle,
      formatTime,
      calculateSuccessRate
    }
  }
}
</script>

<style scoped>
.yaoguang-star-page {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
}

.page-title {
  font-size: 2.5rem;
  color: #2c3e50;
  margin-bottom: 10px;
}

.page-title i {
  color: #f39c12;
  margin-right: 10px;
}

.page-subtitle {
  color: #7f8c8d;
  font-size: 1.1rem;
}

.statistics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
}

.stat-icon {
  font-size: 2.5rem;
  margin-right: 20px;
  width: 60px;
  text-align: center;
}

.learning-card .stat-icon { color: #3498db; }
.factor-card .stat-icon { color: #e74c3c; }
.research-card .stat-icon { color: #2ecc71; }
.practice-card .stat-icon { color: #f39c12; }
.factor-count-card .stat-icon { color: #9b59b6; }
.quality-card .stat-icon { color: #1abc9c; }

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 2rem;
  font-weight: bold;
  color: #2c3e50;
}

.stat-label {
  font-size: 1rem;
  color: #7f8c8d;
  margin: 5px 0;
}

.stat-today {
  font-size: 0.9rem;
  color: #95a5a6;
}

.automation-status {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.status-header h3 {
  margin: 0;
  color: #2c3e50;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.btn-primary {
  background: #3498db;
  color: white;
}

.btn-primary:hover {
  background: #2980b9;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-success {
  background: #27ae60;
  color: white;
}

.btn-success:hover {
  background: #229954;
}

.btn-warning {
  background: #f39c12;
  color: white;
}

.btn-warning:hover {
  background: #e67e22;
}

.btn-info {
  background: #3498db;
  color: white;
}

.btn-info:hover {
  background: #2980b9;
}

/* 学习模式控制面板 */
.learning-control-panel {
  background: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
  border-left: 4px solid #3498db;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 15px;
}

.panel-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.4rem;
}

.panel-controls {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.learning-status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-bottom: 25px;
}

.real-time-logs {
  margin-top: 25px;
}

.logs-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.logs-header h4 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.2rem;
}

.logs-container {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 15px;
  max-height: 300px;
  overflow-y: auto;
}

.log-entries {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.log-entry {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px 12px;
  border-radius: 6px;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  border-left: 3px solid transparent;
}

.log-entry.info {
  background: #e3f2fd;
  border-left-color: #2196f3;
}

.log-entry.success {
  background: #e8f5e8;
  border-left-color: #4caf50;
}

.log-entry.error {
  background: #ffebee;
  border-left-color: #f44336;
}

.log-entry.warning {
  background: #fff3e0;
  border-left-color: #ff9800;
}

.log-entry .log-time {
  color: #666;
  font-size: 0.8rem;
  min-width: 80px;
}

.log-entry .log-level {
  font-weight: bold;
  min-width: 60px;
  font-size: 0.8rem;
}

.log-entry.info .log-level { color: #2196f3; }
.log-entry.success .log-level { color: #4caf50; }
.log-entry.error .log-level { color: #f44336; }
.log-entry.warning .log-level { color: #ff9800; }

.log-entry .log-message {
  flex: 1;
  word-break: break-word;
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 6px;
}

.status-label {
  color: #7f8c8d;
}

.status-value {
  font-weight: bold;
  color: #2c3e50;
}

.status-value.running {
  color: #27ae60;
}

.status-value.stopped {
  color: #e74c3c;
}

.log-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.log-dialog {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 800px;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}

.log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #ecf0f1;
}

.log-header h3 {
  margin: 0;
  color: #2c3e50;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.2rem;
  cursor: pointer;
  color: #7f8c8d;
}

.close-btn:hover {
  color: #2c3e50;
}

.log-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

.loading, .no-logs {
  text-align: center;
  color: #7f8c8d;
  padding: 40px;
}

.log-list {
  space-y: 10px;
}

.log-item {
  border: 1px solid #ecf0f1;
  border-radius: 6px;
  margin-bottom: 10px;
}

.log-summary {
  display: flex;
  align-items: center;
  padding: 15px;
  cursor: pointer;
  transition: background 0.3s ease;
}

.log-summary:hover {
  background: #f8f9fa;
}

.log-time {
  font-size: 0.9rem;
  color: #7f8c8d;
  width: 150px;
}

.log-status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: bold;
  width: 80px;
  text-align: center;
}

.log-status.completed {
  background: #d4edda;
  color: #155724;
}

.log-status.failed {
  background: #f8d7da;
  color: #721c24;
}

.log-summary-text {
  flex: 1;
  margin: 0 15px;
  color: #2c3e50;
}

.expand-icon {
  color: #7f8c8d;
  transition: transform 0.3s ease;
}

.expand-icon.expanded {
  transform: rotate(180deg);
}

.log-details {
  padding: 15px;
  background: #f8f9fa;
  border-top: 1px solid #ecf0f1;
}

.log-details pre {
  margin: 0;
  font-size: 0.8rem;
  color: #2c3e50;
  white-space: pre-wrap;
  word-wrap: break-word;
}

/* 报告样式 */
.factor-research-report, .learning-report {
  padding: 0;
}

.report-section {
  margin-bottom: 20px;
}

.report-section h4 {
  margin: 0 0 15px 0;
  color: #2c3e50;
  font-size: 1.1rem;
  border-bottom: 2px solid #3498db;
  padding-bottom: 5px;
}

.report-section h5 {
  margin: 15px 0 10px 0;
  color: #34495e;
  font-size: 1rem;
}

.report-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 10px;
  margin-bottom: 15px;
}

.report-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 4px;
  border-left: 3px solid #3498db;
}

.report-item .label {
  font-weight: 500;
  color: #7f8c8d;
}

.report-item .value {
  font-weight: bold;
  color: #2c3e50;
}

.factor-details {
  margin-top: 15px;
}

.stock-factor {
  margin-bottom: 10px;
  padding: 10px;
  background: #f1f2f6;
  border-radius: 6px;
}

.stock-header {
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 5px;
}

.factor-stats {
  display: flex;
  gap: 15px;
  font-size: 0.9rem;
  color: #7f8c8d;
}

.key-learnings, .recommendations {
  margin-top: 15px;
}

.key-learnings ul, .recommendations ul {
  margin: 10px 0;
  padding-left: 20px;
}

.key-learnings li, .recommendations li {
  margin-bottom: 8px;
  color: #2c3e50;
  line-height: 1.4;
}

.raw-details {
  max-height: 300px;
  overflow-y: auto;
}

.log-footer {
  padding: 20px;
  border-top: 1px solid #ecf0f1;
  text-align: center;
}

.btn-sm {
  padding: 6px 12px;
  font-size: 0.8rem;
}

@media (max-width: 768px) {
  .statistics-grid {
    grid-template-columns: 1fr;
  }
  
  .status-grid {
    grid-template-columns: 1fr;
  }
  
  .log-dialog {
    width: 95%;
    max-height: 90vh;
  }
  
  .log-summary {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .log-time, .log-status {
    width: auto;
    margin-bottom: 5px;
  }
}
</style>
