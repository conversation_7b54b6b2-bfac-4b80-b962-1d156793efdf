<template>
  <div class="yaoguang-star-main">
    <!-- 瑶光星学习系统主页面 -->
    <div class="page-header">
      <div class="header-content">
        <div class="star-info">
          <div class="star-icon">🌟</div>
          <div class="star-details">
            <h1>瑶光星学习系统</h1>
            <p>智能量化研究与自动化学习平台</p>
          </div>
        </div>
        <div class="system-status" :class="systemStatus.class">
          <span class="status-indicator"></span>
          <span>{{ systemStatus.text }}</span>
        </div>
      </div>
    </div>

    <!-- 智能调度控制面板 -->
    <div class="scheduler-control-panel">
      <div class="panel-header">
        <h3>🤖 智能自动化调度系统</h3>
        <div class="scheduler-status-badge" :class="schedulerStatus.is_running ? 'running' : 'stopped'">
          {{ schedulerStatus.is_running ? '运行中' : '已停止' }}
        </div>
      </div>

      <div class="scheduler-controls">
        <button
          class="btn btn-primary"
          @click="startScheduler"
          :disabled="schedulerStatus.is_running || schedulerLoading"
        >
          <i class="fas fa-play"></i>
          启动智能调度器
        </button>
        <button
          class="btn btn-danger"
          @click="stopScheduler"
          :disabled="!schedulerStatus.is_running || schedulerLoading"
        >
          <i class="fas fa-stop"></i>
          停止智能调度器
        </button>
        <button
          class="btn btn-info"
          @click="refreshSchedulerStatus"
          :disabled="schedulerLoading"
        >
          <i class="fas fa-sync"></i>
          刷新状态
        </button>
      </div>

      <div class="scheduler-status-grid">
        <div class="status-item">
          <span class="status-label">当前模式:</span>
          <span class="status-value">{{ getModeName(schedulerStatus.current_mode) }}</span>
        </div>
        <div class="status-item">
          <span class="status-label">市场状态:</span>
          <span class="status-value">{{ getMarketStatusName(schedulerStatus.market_status) }}</span>
        </div>
        <div class="status-item">
          <span class="status-label">是否交易日:</span>
          <span class="status-value">{{ schedulerStatus.is_trading_day ? '是' : '否' }}</span>
        </div>
        <div class="status-item">
          <span class="status-label">当前时间:</span>
          <span class="status-value">{{ getCurrentTime() }}</span>
        </div>
        <div class="status-item">
          <span class="status-label">应运行模式:</span>
          <span class="status-value">{{ schedulerStatus.should_run_live_trading ? '实盘交易' : '学习模式' }}</span>
        </div>
        <div class="status-item">
          <span class="status-label">今日统计:</span>
          <span class="status-value">学习{{ schedulerStatus.daily_stats?.learning_sessions || 0 }}次 交易{{ schedulerStatus.daily_stats?.live_trading_sessions || 0 }}次</span>
        </div>
      </div>

      <div class="scheduler-alert">
        <div v-if="schedulerStatus.should_run_live_trading" class="alert alert-success">
          <i class="fas fa-chart-line"></i>
          当前应运行实盘交易模式
        </div>
        <div v-else class="alert alert-info">
          <i class="fas fa-graduation-cap"></i>
          当前应运行学习模式
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import axios from 'axios'

console.log('🌟 瑶光星学习系统页面加载')

// 系统状态
const systemStatus = ref({
  class: 'status-running',
  text: '系统正常运行'
})

// 调度器状态
const schedulerStatus = ref({
  is_running: false,
  current_mode: 'idle',
  market_status: 'closed',
  is_trading_day: false,
  should_run_live_trading: false,
  should_run_learning: false,
  current_time: null,
  daily_stats: {
    learning_sessions: 0,
    live_trading_sessions: 0,
    total_trades: 0,
    total_pnl: 0.0
  },
  tasks_status: {
    learning_task: 'stopped',
    live_trading_task: 'stopped',
    scheduler_task: 'stopped'
  }
})

// 加载状态
const schedulerLoading = ref(false)

// 智能调度相关方法
const loadSchedulerStatus = async () => {
  try {
    const response = await axios.get('/api/yaoguang-star/scheduler/status')
    if (response.data.success) {
      Object.assign(schedulerStatus.value, response.data.data)
    }
  } catch (error) {
    console.error('加载调度器状态失败:', error)
  }
}

const startScheduler = async () => {
  schedulerLoading.value = true
  try {
    const response = await axios.post('/api/yaoguang-star/scheduler/start')
    if (response.data.success) {
      await loadSchedulerStatus()
      console.log('✅ 智能调度器启动成功')
    }
  } catch (error) {
    console.error('启动调度器失败:', error)
  } finally {
    schedulerLoading.value = false
  }
}

const stopScheduler = async () => {
  schedulerLoading.value = true
  try {
    const response = await axios.post('/api/yaoguang-star/scheduler/stop')
    if (response.data.success) {
      await loadSchedulerStatus()
      console.log('✅ 智能调度器停止成功')
    }
  } catch (error) {
    console.error('停止调度器失败:', error)
  } finally {
    schedulerLoading.value = false
  }
}

const refreshSchedulerStatus = async () => {
  schedulerLoading.value = true
  try {
    await loadSchedulerStatus()
    console.log('🔄 调度器状态已刷新')
  } catch (error) {
    console.error('刷新调度器状态失败:', error)
  } finally {
    schedulerLoading.value = false
  }
}

const getModeName = (mode: string) => {
  const modeNames = {
    'learning': '学习模式',
    'live_trading': '实盘交易',
    'idle': '空闲'
  }
  return modeNames[mode] || mode
}

const getMarketStatusName = (status: string) => {
  const statusNames = {
    'pre_market': '盘前',
    'trading': '交易中',
    'post_market': '盘后',
    'closed': '休市'
  }
  return statusNames[status] || status
}

const getCurrentTime = () => {
  return new Date().toLocaleString('zh-CN')
}

onMounted(async () => {
  console.log('🌟 瑶光星学习系统页面挂载完成')

  // 加载调度器状态
  await loadSchedulerStatus()

  // 定时刷新调度器状态
  setInterval(loadSchedulerStatus, 10000) // 10秒刷新一次
})

onUnmounted(() => {
  console.log('🌟 瑶光星学习系统页面卸载')
})


</script>

<style lang="scss" scoped>
.yaoguang-star-main {
  width: 100%;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  box-sizing: border-box;
}

.page-header {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  padding: 25px;
  margin-bottom: 30px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.star-info {
  display: flex;
  align-items: center;
  gap: 20px;
}

.star-icon {
  font-size: 3rem;
  background: linear-gradient(45deg, #ffd700, #ffed4e);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  filter: drop-shadow(0 2px 4px rgba(255, 215, 0, 0.3));
}

.star-details h1 {
  margin: 0;
  font-size: 2.5rem;
  background: linear-gradient(45deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: 700;
}

.star-details p {
  margin: 5px 0 0 0;
  color: #666;
  font-size: 1.1rem;
}

.system-status {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 20px;
  font-weight: 500;
}

.status-running {
  background: #d4edda;
  color: #155724;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: currentColor;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* 智能调度控制面板 */
.scheduler-control-panel {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  padding: 25px;
  margin-bottom: 30px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border-left: 4px solid #9b59b6;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.panel-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.5rem;
}

.scheduler-status-badge {
  padding: 6px 16px;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
}

.scheduler-status-badge.running {
  background: #d4edda;
  color: #155724;
}

.scheduler-status-badge.stopped {
  background: #f8d7da;
  color: #721c24;
}

.scheduler-controls {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  margin-bottom: 25px;
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: linear-gradient(45deg, #667eea, #764ba2);
  color: white;
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.btn-danger {
  background: linear-gradient(45deg, #ff6b6b, #ee5a52);
  color: white;
}

.btn-danger:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(255, 107, 107, 0.4);
}

.btn-info {
  background: linear-gradient(45deg, #4ecdc4, #44a08d);
  color: white;
}

.btn-info:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(78, 205, 196, 0.4);
}

.scheduler-status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
  margin-bottom: 25px;
}

.status-item {
  background: rgba(102, 126, 234, 0.1);
  padding: 15px;
  border-radius: 8px;
  border-left: 3px solid #667eea;
}

.status-label {
  display: block;
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 5px;
}

.status-value {
  display: block;
  font-size: 1.1rem;
  font-weight: 600;
  color: #2c3e50;
}

.scheduler-alert {
  margin-top: 20px;
}

.alert {
  padding: 15px 20px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 12px;
  font-weight: 500;
}

.alert-success {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.alert-info {
  background: #d1ecf1;
  color: #0c5460;
  border: 1px solid #bee5eb;
}

.alert i {
  font-size: 1.2rem;
}
</style>
